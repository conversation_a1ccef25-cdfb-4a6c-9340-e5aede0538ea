* {
    margin: 0;
    padding: 0;
    text-decoration: none;
    list-style: none;
    box-sizing: border-box;
    font-size: 16px;
    line-height: 1.6;
}

body {
    margin: 0;
    font-family: Arial, sans-serif;
    text-align: center;
}

.weather-app {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background-color: #c2e3e8;
}

h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

section {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

button {
    padding: 0.5rem 1rem;
    font-size: 1rem;
    cursor: pointer;
    border: none;
    border-radius: 0.5rem;
    background-color: #007bff;
    color: white;
    transition: background-color 0.3s ease;
    margin-top: 1rem;
}
form {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
    width: 40%;
}

input {
    padding: 0.5rem 1rem;
    font-size: 1rem;
    border: 1px solid #ccc;
    border-radius: 0.5rem;
    margin-top: 1rem;
    width: 100%;
}
.weather-result-container{
    margin-top: 1rem;
    padding: 1rem;
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    text-align: left;
    width: 40%;
    min-height: 200px;
    color: #3333338e;
}
@media screen and (max-width: 768px) {
    form,.weather-result-container{
        width: 80%;
    }
}