# 學習會話記錄 - API 錯誤除錯與解決

## 會話資訊
- **日期**：2025-01-08
- **會話編號**：006
- **類型**：API 除錯與錯誤處理
- **用戶**：Sandy

## 問題描述
用戶在開發天氣API功能時遇到兩個問題：
1. fetch(apiUrl) 出現 401 Unauthorized 錯誤
2. 錯誤處理函數不能運作

## 問題分析與解決方案

### 🚨 問題一：401 Unauthorized 錯誤

#### 原因分析
401錯誤表示「未授權」，通常是以下原因：
1. API Key 無效或格式錯誤
2. API URL 需要使用 HTTPS 而非 HTTP
3. API Key 參數名稱錯誤
4. API Key 權限不足

#### 解決方案
1. **檢查 API Key 有效性**
2. **修正 API URL 格式**
3. **使用 HTTPS 協議**
4. **加強錯誤處理**

### 🔧 問題二：錯誤處理函數不能運作

#### 原因分析
1. displayError 函數未定義
2. try-catch 沒有正確捕捉錯誤
3. 錯誤處理邏輯有問題

#### 解決方案
1. **實作完整的錯誤處理函數**
2. **改善 try-catch 結構**
3. **加入詳細的錯誤日誌**

## 修正後的程式碼

### 完整的錯誤處理函數
```javascript
// 錯誤處理函數
const displayError = (message) => {
    console.error('錯誤:', message);
    
    if (weatherResult) {
        weatherResult.innerHTML = `
            <div class="error-message" style="color: red; padding: 10px; border: 1px solid red; border-radius: 5px;">
                <p>❌ ${message}</p>
                <p>請檢查城市名稱是否正確，或稍後再試。</p>
            </div>
        `;
    } else {
        alert(`錯誤: ${message}`);
    }
};

// 載入中狀態
const showLoading = () => {
    if (weatherResult) {
        weatherResult.innerHTML = `
            <div class="loading" style="color: blue; padding: 10px;">
                <p>🔄 正在查詢天氣資訊...</p>
            </div>
        `;
    }
};
```

### 修正的 API 呼叫函數
```javascript
async function getCityCoordinates(cityName) {
    // 使用 HTTPS 而非 HTTP
    const apiUrl = `https://api.openweathermap.org/geo/1.0/direct?q=${cityName},TW&limit=1&appid=${API_KEY}`;
    
    console.log('API URL:', apiUrl); // 除錯用
    
    try {
        showLoading();
        
        const response = await fetch(apiUrl);
        
        console.log('Response status:', response.status); // 除錯用
        console.log('Response ok:', response.ok); // 除錯用
        
        if (!response.ok) {
            // 根據不同的錯誤狀態碼提供具體訊息
            let errorMessage;
            switch (response.status) {
                case 401:
                    errorMessage = 'API Key 無效或未授權';
                    break;
                case 404:
                    errorMessage = '找不到指定的城市';
                    break;
                case 429:
                    errorMessage = 'API 呼叫次數超過限制';
                    break;
                default:
                    errorMessage = `HTTP 錯誤！狀態碼: ${response.status}`;
            }
            throw new Error(errorMessage);
        }
        
        const data = await response.json();
        console.log('API 回應資料:', data); // 除錯用
        
        if (data && data.length > 0) {
            const { lat, lon } = data[0];
            console.log(`找到座標: lat=${lat}, lon=${lon}`); // 除錯用
            return { lat, lon };
        } else {
            throw new Error(`找不到城市: ${cityName}`);
        }
        
    } catch (error) {
        console.error('API 呼叫錯誤:', error);
        displayError(error.message);
        return null;
    }
}
```

## 除錯步驟

### 步驟 1：檢查 API Key
```javascript
// 在 Debug Console 中執行
console.log('API Key:', API_KEY);
console.log('API Key 長度:', API_KEY.length);
console.log('API Key 是否包含特殊字符:', /[^a-zA-Z0-9]/.test(API_KEY));
```

### 步驟 2：測試 API URL
```javascript
// 手動測試 API URL
const testUrl = `https://api.openweathermap.org/geo/1.0/direct?q=Taipei,TW&limit=1&appid=${API_KEY}`;
console.log('測試 URL:', testUrl);

// 在瀏覽器中直接訪問這個 URL 檢查回應
```

### 步驟 3：檢查網路請求
```javascript
// 使用 fetch 測試
fetch(testUrl)
    .then(response => {
        console.log('Status:', response.status);
        console.log('Headers:', response.headers);
        return response.text();
    })
    .then(text => {
        console.log('Raw response:', text);
    })
    .catch(error => {
        console.error('Fetch error:', error);
    });
```

## 常見問題與解決方法

### ❌ 問題：API Key 無效
**解決方法：**
1. 檢查 OpenWeatherMap 帳號是否已啟用
2. 確認 API Key 是否正確複製
3. 檢查 API Key 是否有地理編碼權限

### ❌ 問題：CORS 錯誤
**解決方法：**
1. 使用 HTTPS 而非 HTTP
2. 確保在正確的網域下測試
3. 考慮使用代理伺服器

### ❌ 問題：網路連線問題
**解決方法：**
1. 檢查網路連線
2. 嘗試使用不同的 DNS
3. 檢查防火牆設定

## 學習重點

### 🎯 HTTP 狀態碼理解
- 200-299: 成功
- 400-499: 客戶端錯誤
- 500-599: 伺服器錯誤

### 🔧 錯誤處理最佳實踐
1. 使用具體的錯誤訊息
2. 記錄詳細的除錯資訊
3. 提供使用者友善的錯誤提示
4. 實作重試機制

### 📊 API 除錯技巧
1. 先在瀏覽器中測試 API URL
2. 檢查 Network 面板的請求詳情
3. 使用 console.log 記錄關鍵資訊
4. 逐步測試每個環節

## Tags
#api-debugging #error-handling #http-status-codes #fetch-api #openweathermap #javascript #troubleshooting

## 問題類型
- API 整合問題
- 錯誤處理
- 除錯技巧

## 解決狀態
- 問題分析完成 ✅
- 解決方案提供 ✅
- 修正程式碼準備完成 ✅
