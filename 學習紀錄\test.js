// ES6 語法學習測試檔案
// 從 main.js 提取的測試代碼，用於學習 JavaScript ES6 語法

console.log("=== ES6 語法學習測試開始 ===");

// 1. 變數定義 (const, let, var 的差異)
console.log("=== 變數定義測試 ===");
// const：只在區塊中有效，避免重新賦值影響全域功能
const API_URL_EXAMPLE = "https://api.example.com/v1";
console.log("API URL 範例:", API_URL_EXAMPLE);

// let：只在區塊中有效，可以重新賦值
// var：全域有效，可以重新賦值
// 區塊作用域：大括號內容

// 2. 迴圈測試
console.log("=== 迴圈測試 ===");
for (let i = 0; i < 5; i++) {
    console.log("在迴圈內:", i);
}

// 3. Array.map() 測試 - ES6 箭頭函數
console.log("=== Map 測試 ===");
const users = [
    { name: "<PERSON>", age: 30 },
    { name: "<PERSON>", age: 28 },
    { name: "<PERSON>", age: 32 },
];

// 使用箭頭函數提取使用者名稱
const userNames = users.map(user => user.name);
console.log("Users:", userNames);

// 4. Array.filter() 測試 - ES6 箭頭函數
console.log("=== Filter 測試 ===");
const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
const evenNumbers = numbers.filter(num => {
    // 測試函式：如果數字除以2的餘數為0，就回傳 true
    return num % 2 === 0;
});
console.log("偶數:", evenNumbers);

// 5. 事件處理器 - ES6 箭頭函數
console.log("=== 事件處理器測試 ===");
const handleTaskSubmit = (event) => {
    // 防止任務執行時刷新頁面
    event.preventDefault();
    alert("任務完成！");
};

// 6. 環境檢查 - 條件判斷
console.log("=== 環境檢查測試 ===");
if (typeof document !== 'undefined') {
    console.log("瀏覽器環境 - 設定事件監聽器");
    document.addEventListener('DOMContentLoaded', () => {
        const taskForm = document.getElementById("task-form");
        if (taskForm) {
            taskForm.addEventListener("submit", handleTaskSubmit);
            console.log("表單事件監聽器已設定");
        }
    });
} else {
    console.log("Node.js 環境 - 跳過 DOM 操作");
}

// 7. ES6 解構賦值範例
console.log("=== 解構賦值測試 ===");
const firstUser = users[0];
const { name, age } = firstUser;
console.log(`解構賦值結果: 姓名=${name}, 年齡=${age}`);

// 8. ES6 模板字串範例
console.log("=== 模板字串測試 ===");
const greeting = `Hello, ${name}! You are ${age} years old.`;
console.log(greeting);

// 9. ES6 展開運算符範例
console.log("=== 展開運算符測試 ===");
const moreNumbers = [11, 12, 13];
const allNumbers = [...numbers, ...moreNumbers];
console.log("合併陣列:", allNumbers);

// 10. ES6 預設參數範例
console.log("=== 預設參數測試 ===");
const greetUser = (name = "Guest", age = 0) => {
    return `Hello, ${name}! Age: ${age}`;
};
console.log(greetUser());
console.log(greetUser("Sandy", 25));

console.log("=== ES6 語法學習測試結束 ===");

// 學習重點筆記：
// 1. const vs let vs var 的作用域差異
// 2. 箭頭函數的簡潔語法
// 3. Array.map() 和 Array.filter() 的函數式編程
// 4. 模板字串的便利性
// 5. 解構賦值的實用性
// 6. 展開運算符的陣列操作
// 7. 預設參數的函數設計
