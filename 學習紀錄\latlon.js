// API 配置
const API_KEY = '5592edd7014015b3ff5d0efedbb3690c';

// DOM 元素引用
let searchForm;
let cityInput;
let weatherResult;

// 錯誤處理函數
const displayError = (message) => {
    console.error('錯誤:', message);

    if (weatherResult) {
        weatherResult.innerHTML = `
            <div class="error-message" style="color: red; padding: 10px; border: 1px solid red; border-radius: 5px; background-color: #ffe6e6;">
                <p>❌ ${message}</p>
                <p>請檢查城市名稱是否正確，或稍後再試。</p>
            </div>
        `;
    } else {
        alert(`錯誤: ${message}`);
    }
};

// 載入中狀態
const showLoading = () => {
    if (weatherResult) {
        weatherResult.innerHTML = `
            <div class="loading" style="color: blue; padding: 10px; background-color: #e6f3ff; border-radius: 5px;">
                <p>🔄 正在查詢天氣資訊...</p>
            </div>
        `;
    }
};

/**
 * 根據城市名稱，取得該城市的經緯度
 * @param {string} cityName - 要查詢的城市英文名稱 (例如: 'Taoyuan')
 * @returns {Promise<object|null>} - 回傳一個包含經緯度的物件 { lat, lon }，或是在找不到城市或發生錯誤時回傳 null
 */
async function getCityCoordinates(cityName) {
    // 1. 使用模板字串動態組合 API 網址 (使用 HTTPS)
    const apiUrl = `http://api.openweathermap.org/geo/1.0/direct?q=${cityName},TW&limit=1&appid=${API_KEY}`;

    console.log('API URL:', apiUrl); // 除錯用：檢查 URL 格式

    // 2. 使用 try...catch 結構來捕捉潛在的錯誤 (例如網路問題)
    try {
        // 3. 使用 await 等待 fetch 請求完成，並取得回應 (response)
        const response = await fetch(apiUrl);

        console.log('Response status:', response.status); // 除錯用：檢查狀態碼
        console.log('Response ok:', response.ok); // 除錯用：檢查是否成功

        // 檢查 API 是否請求成功
        if (!response.ok) {
            // 根據不同的錯誤狀態碼提供具體訊息
            let errorMessage;
            switch (response.status) {
                case 401:
                    errorMessage = 'API Key 無效或未授權 - 請檢查 API Key 是否正確';
                    break;
                case 404:
                    errorMessage = '找不到指定的城市';
                    break;
                case 429:
                    errorMessage = 'API 呼叫次數超過限制 - 請稍後再試';
                    break;
                default:
                    errorMessage = `HTTP 錯誤！狀態碼: ${response.status}`;
            }
            throw new Error(errorMessage);
        }
        // 4. 使用 await 等待將回應的 body 解析成 JSON 格式
        const data = await response.json();
        // 5. 檢查 API 是否回傳了有效的資料
        if (data && data.length > 0) {
            // 6. 使用「解構賦值」從回傳的第一筆資料中取出 lat 和 lon
            const { lat, lon } = data[0];
            // 7. 回傳包含經緯度的物件
            return { lat, lon };
        } else {
            // 如果 API 回傳空陣列，表示找不到該城市
            console.warn(`找不到城市: ${cityName}`);
            return null;
        }
    } catch (error) {
        // 如果在 try 區塊中發生任何錯誤，都會在這裡被捕捉
        console.error('取得資料時發生錯誤:', error);
        return null; // 發生錯誤時也回傳 null
    }
}



// 表單提交處理
const handleWeatherSearch = (event) => {
    event.preventDefault();

    const cityName = cityInput.value.trim();

    if (!cityName) {
        displayError('請輸入城市名稱');
        return;
    }

    if (API_KEY === 'API_KEY') {
        displayError('請先設定 API Key');
        return;
    }
    // --- 如何使用這個函式 ---
    // 我們建立另一個 async 函式來呼叫 getCityCoordinates
    async function main() {
        console.log('正在查詢台北的經緯度...');
        const taipeiCoords = await getCityCoordinates('Taipei');
        if (taipeiCoords) {
            console.log(`台北的經緯度: Latitude: ${taipeiCoords.lat}, Longitude: ${taipeiCoords.lon}`);
        }

        console.log('正在查詢高雄的經緯度...');
        const kaohsiungCoords = await getCityCoordinates('Kaohsiung');
        if (kaohsiungCoords) {
            console.log(`高雄的經緯度: Latitude: ${kaohsiungCoords.lat}, Longitude: ${kaohsiungCoords.lon}`);
        }

        console.log('正在查詢一個不存在的城市...');
        const fakeCityCoords = await getCityCoordinates('FakeCity');
        if (!fakeCityCoords) {
            console.log('查詢失敗，正如預期。');
        }
    }
    // 執行主函式
    main();
};
// 初始化應用程式
const initWeatherApp = () => {
    // 取得 DOM 元素
    searchForm = document.getElementById('search-form');
    cityInput = document.getElementById('city-input');
    weatherResult = document.getElementById('weather-result');

    // 檢查必要元素是否存在
    if (!searchForm || !cityInput || !weatherResult) {
        console.error('找不到必要的 DOM 元素');
        return;
    }

    // 設定事件監聽器
    searchForm.addEventListener('submit', handleWeatherSearch);

    console.log('天氣應用程式初始化完成');
};

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', initWeatherApp);