# 🌤️ Weather API 天氣查詢應用

一個簡潔的天氣查詢網頁應用，使用 OpenWeatherMap API 提供即時天氣資訊。

## 📋 專案概述

這是一個練習 API 串接的前端專案，主要功能包括：
- 🔍 城市天氣查詢
- 🌡️ 溫度、濕度、風速顯示
- 📱 響應式設計
- ⚡ 即時資料更新

## 🚀 功能特色

- **簡潔介面**：使用 Tailwind CSS 設計的現代化 UI
- **即時查詢**：輸入城市名稱即可查詢當前天氣
- **詳細資訊**：顯示溫度、天氣描述、濕度、風速等資訊
- **錯誤處理**：友善的錯誤提示和載入狀態
- **中文支援**：支援中文天氣描述

## 🛠️ 技術棧

- **前端**：HTML5, CSS3, JavaScript (ES6+)
- **樣式框架**：Tailwind CSS
- **API**：OpenWeatherMap API
- **開發工具**：VS Code

## 📁 專案結構

```
weather-api-project/
├── index.html          # 主頁面
├── main.js             # 天氣 API 邏輯
├── style.css           # 自定義樣式
├── test.js             # ES6 語法學習檔案
├── W3C.md              # HTML 實體編碼參考
├── README.md           # 專案說明
└── learning-records/   # 學習記錄系統
```

## ⚙️ 安裝與設定

### 1. 取得 API Key
1. 前往 [OpenWeatherMap](https://openweathermap.org/api) 註冊帳號
2. 取得免費的 API Key

### 2. 設定 API Key
在 `main.js` 檔案中，將 `YOUR_API_KEY_HERE` 替換為你的實際 API Key：

```javascript
const WEATHER_API_KEY = 'your_actual_api_key_here';
```

### 3. 執行專案
1. 直接在瀏覽器中開啟 `index.html`
2. 或使用 Live Server 等開發工具

## 🎯 使用方法

1. 在搜尋框中輸入城市英文名稱（例如：Taipei, Tokyo, London）
2. 點擊「查詢」按鈕
3. 查看天氣資訊結果

## 📚 學習重點

這個專案涵蓋了以下前端開發技能：

### JavaScript ES6+
- `async/await` 非同步處理
- `fetch()` API 呼叫
- 箭頭函數
- 模板字串
- 解構賦值

### DOM 操作
- 事件監聽器
- 動態內容更新
- 表單處理
- 錯誤處理

### API 整合
- RESTful API 呼叫
- JSON 資料處理
- 錯誤狀態管理
- 載入狀態顯示

## 🔧 開發筆記

### API 回應格式
```json
{
  "name": "Taipei",
  "main": {
    "temp": 298.15,
    "humidity": 65
  },
  "weather": [
    {
      "description": "晴朗"
    }
  ],
  "wind": {
    "speed": 3.5
  }
}
```

### 溫度轉換
OpenWeatherMap API 回傳的溫度單位為 Kelvin，需要轉換為攝氏度：
```javascript
const celsius = kelvin - 273.15;
```

## 🚨 注意事項

1. **API Key 安全性**：請勿將 API Key 提交到公開的版本控制系統
2. **API 限制**：免費版本有每分鐘呼叫次數限制
3. **城市名稱**：請使用英文城市名稱查詢
4. **網路連線**：需要網路連線才能查詢天氣資訊

## 🎓 學習資源

- [OpenWeatherMap API 文件](https://openweathermap.org/api)
- [Fetch API MDN 文件](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API)
- [Tailwind CSS 文件](https://tailwindcss.com/docs)

## 📝 待改進功能

- [ ] 支援地理位置自動偵測
- [ ] 5天天氣預報
- [ ] 天氣圖示顯示
- [ ] 本地儲存最近查詢記錄
- [ ] 多語言支援

## 👨‍💻 開發者

**Sandy** - 前端學習者

---

*這是一個學習專案，用於練習 JavaScript API 串接和前端開發技能。*
