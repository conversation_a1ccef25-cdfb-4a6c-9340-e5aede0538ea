// API 測試檔案 - 用於除錯和驗證
console.log("=== API 測試開始 ===");

// 測試用的 API Key (請確保這個值正確)
const TEST_API_KEY = '5592edd7014015b3ff5d0efedbb3690c';

// 測試函數 1：檢查 API Key 格式
function testApiKeyFormat() {
    console.log("--- 測試 1: API Key 格式檢查 ---");
    console.log('API Key:', TEST_API_KEY);
    console.log('API Key 長度:', TEST_API_KEY.length);
    console.log('API Key 是否只包含英數字:', /^[a-zA-Z0-9]+$/.test(TEST_API_KEY));
    
    if (TEST_API_KEY.length !== 32) {
        console.warn('⚠️ API Key 長度不是標準的 32 字元');
    }
}

// 測試函數 2：直接測試 API URL
async function testApiUrl() {
    console.log("--- 測試 2: API URL 直接測試 ---");
    
    const testCity = 'Taipei';
    const apiUrl = `https://api.openweathermap.org/geo/1.0/direct?q=${testCity},TW&limit=1&appid=${TEST_API_KEY}`;
    
    console.log('測試 URL:', apiUrl);
    
    try {
        const response = await fetch(apiUrl);
        
        console.log('Response status:', response.status);
        console.log('Response statusText:', response.statusText);
        console.log('Response ok:', response.ok);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ API 呼叫成功！');
            console.log('回應資料:', data);
            
            if (data && data.length > 0) {
                const { lat, lon, name } = data[0];
                console.log(`城市: ${name}, 緯度: ${lat}, 經度: ${lon}`);
            }
        } else {
            const errorText = await response.text();
            console.error('❌ API 呼叫失敗');
            console.error('錯誤內容:', errorText);
        }
        
    } catch (error) {
        console.error('❌ 網路錯誤:', error);
    }
}

// 測試函數 3：測試不同城市
async function testMultipleCities() {
    console.log("--- 測試 3: 多個城市測試 ---");
    
    const cities = ['Taipei', 'Kaohsiung', 'Taichung', 'Tainan'];
    
    for (const city of cities) {
        console.log(`\n測試城市: ${city}`);
        
        const apiUrl = `https://api.openweathermap.org/geo/1.0/direct?q=${city},TW&limit=1&appid=${TEST_API_KEY}`;
        
        try {
            const response = await fetch(apiUrl);
            
            if (response.ok) {
                const data = await response.json();
                if (data && data.length > 0) {
                    const { lat, lon, name } = data[0];
                    console.log(`✅ ${name}: lat=${lat}, lon=${lon}`);
                } else {
                    console.log(`❌ 找不到城市: ${city}`);
                }
            } else {
                console.log(`❌ HTTP 錯誤: ${response.status}`);
            }
            
        } catch (error) {
            console.log(`❌ 網路錯誤: ${error.message}`);
        }
        
        // 避免 API 呼叫過於頻繁
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
}

// 測試函數 4：錯誤處理測試
async function testErrorHandling() {
    console.log("--- 測試 4: 錯誤處理測試 ---");
    
    // 測試無效的 API Key
    console.log('\n測試無效 API Key:');
    const invalidApiUrl = `https://api.openweathermap.org/geo/1.0/direct?q=Taipei,TW&limit=1&appid=invalid_key`;
    
    try {
        const response = await fetch(invalidApiUrl);
        console.log('Status:', response.status);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.log('預期的錯誤:', errorText);
        }
    } catch (error) {
        console.log('捕捉到錯誤:', error.message);
    }
    
    // 測試不存在的城市
    console.log('\n測試不存在的城市:');
    const nonExistentCityUrl = `https://api.openweathermap.org/geo/1.0/direct?q=NonExistentCity,TW&limit=1&appid=${TEST_API_KEY}`;
    
    try {
        const response = await fetch(nonExistentCityUrl);
        
        if (response.ok) {
            const data = await response.json();
            console.log('不存在城市的回應:', data);
            
            if (data.length === 0) {
                console.log('✅ 正確處理：回傳空陣列');
            }
        }
    } catch (error) {
        console.log('錯誤:', error.message);
    }
}

// 執行所有測試
async function runAllTests() {
    try {
        testApiKeyFormat();
        await testApiUrl();
        await testMultipleCities();
        await testErrorHandling();
        
        console.log("\n=== 所有測試完成 ===");
        
    } catch (error) {
        console.error('測試過程中發生錯誤:', error);
    }
}

// 如果在瀏覽器環境中，等待 DOM 載入完成
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', runAllTests);
} else {
    // 如果在 Node.js 環境中，直接執行
    runAllTests();
}

console.log("測試檔案載入完成，請查看 Console 輸出結果");
