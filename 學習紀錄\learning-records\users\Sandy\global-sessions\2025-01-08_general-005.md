# 學習會話記錄 - VS Code Debug Console 使用教學

## 會話資訊
- **日期**：2025-01-08
- **會話編號**：005
- **類型**：開發工具學習
- **用戶**：Sandy

## 問題描述
用戶想學習如何使用VS Code內建的DEBUG CONSOLE來測試JavaScript檔案，這是一個重要的開發技能，可以大幅提升除錯效率。

## VS Code Debug Console 完整教學

### 🎯 Debug Console 的三種使用方式

#### 1. **瀏覽器環境除錯** (推薦用於前端專案)
#### 2. **Node.js 環境除錯** (推薦用於純JavaScript邏輯測試)
#### 3. **Live Server + Chrome DevTools** (最常用的前端除錯方式)

### 🚀 方法一：瀏覽器環境除錯

#### 步驟 1：安裝必要擴充功能
```
1. Live Server (ritwickdey.liveserver)
2. Debugger for Chrome (已內建在新版VS Code)
```

#### 步驟 2：建立 launch.json 配置檔案
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Chrome",
            "request": "launch",
            "type": "chrome",
            "url": "http://127.0.0.1:5500/index.html",
            "webRoot": "${workspaceFolder}",
            "sourceMaps": true
        }
    ]
}
```

#### 步驟 3：使用流程
1. 在JS檔案中設定中斷點 (點擊行號左側)
2. 啟動 Live Server (右鍵 index.html → Open with Live Server)
3. 按 F5 或點擊除錯按鈕
4. 在 Debug Console 中輸入變數名稱查看值

### 🖥️ 方法二：Node.js 環境除錯

#### 步驟 1：建立 Node.js 配置
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Node.js",
            "request": "launch",
            "type": "node",
            "program": "${file}",
            "console": "integratedTerminal"
        }
    ]
}
```

#### 步驟 2：使用流程
1. 開啟要測試的 .js 檔案
2. 設定中斷點
3. 按 F5 執行除錯
4. 在 Debug Console 中測試變數和函數

### 🔧 方法三：簡易測試 (無需配置)

#### 使用 VS Code 內建終端機
```bash
# 在終端機中執行
node your-file.js

# 或使用 Node.js REPL
node
> // 在這裡輸入 JavaScript 程式碼
```

## 實際操作範例

### 🧪 測試 latlon.js 檔案

#### 方法 A：Node.js 環境測試
```javascript
// 建立測試檔案：test-latlon.js
const { getCityCoordinates } = require('./latlon.js');

async function testFunction() {
    console.log('開始測試...');
    
    const result = await getCityCoordinates('Taipei');
    console.log('台北座標:', result);
    
    const result2 = await getCityCoordinates('Kaohsiung');
    console.log('高雄座標:', result2);
}

testFunction();
```

#### 方法 B：瀏覽器環境測試
```html
<!-- 在 index.html 中加入 -->
<script src="latlon.js"></script>
<script>
    // 在瀏覽器 Console 中測試
    getCityCoordinates('Taipei').then(result => {
        console.log('台北座標:', result);
    });
</script>
```

### 🎯 Debug Console 常用指令

#### 查看變數值
```javascript
// 在 Debug Console 中輸入
cityName
API_KEY
searchForm
```

#### 執行函數
```javascript
// 測試函數
getCityCoordinates('Taipei')
handleWeatherSearch(mockEvent)
```

#### 修改變數值
```javascript
// 臨時修改變數進行測試
cityName = 'Tokyo'
API_KEY = 'test-key'
```

#### 查看物件屬性
```javascript
// 查看物件結構
console.dir(weatherResult)
Object.keys(data)
JSON.stringify(result, null, 2)
```

## 除錯技巧和最佳實踐

### 🔍 設定有效的中斷點
```javascript
// 在關鍵位置設定中斷點
async function getCityCoordinates(cityName) {
    const apiUrl = `...`; // 中斷點 1：檢查 URL 組成
    
    try {
        const response = await fetch(apiUrl); // 中斷點 2：檢查請求
        const data = await response.json(); // 中斷點 3：檢查回應資料
        
        if (data && data.length > 0) {
            const { lat, lon } = data[0]; // 中斷點 4：檢查解構賦值
            return { lat, lon };
        }
    } catch (error) {
        console.error('錯誤:', error); // 中斷點 5：檢查錯誤
    }
}
```

### 📊 使用 console 方法
```javascript
// 不同層級的 console 輸出
console.log('一般資訊:', data);
console.warn('警告訊息:', warning);
console.error('錯誤訊息:', error);
console.table(arrayData); // 表格形式顯示陣列
console.group('API 呼叫'); // 群組化輸出
console.groupEnd();
```

### ⚡ 效能監控
```javascript
// 測量執行時間
console.time('API 呼叫時間');
await getCityCoordinates('Taipei');
console.timeEnd('API 呼叫時間');
```

## 常見問題解決

### ❌ 問題 1：找不到 DOM 元素
```javascript
// 在 Debug Console 中檢查
document.readyState
document.getElementById('search-form')
```

### ❌ 問題 2：API 呼叫失敗
```javascript
// 檢查網路請求
fetch(apiUrl).then(r => r.text()).then(console.log)
```

### ❌ 問題 3：變數未定義
```javascript
// 檢查變數作用域
typeof variableName
window.variableName // 全域變數
```

## 學習重點總結

### 🎯 核心技能
1. **中斷點設定**：在關鍵位置暫停程式執行
2. **變數檢查**：即時查看變數值和物件結構
3. **函數測試**：在除錯環境中直接呼叫函數
4. **錯誤追蹤**：使用 call stack 追蹤錯誤來源

### 📝 最佳實踐
- 在 API 呼叫前後設定中斷點
- 使用 console.log 記錄關鍵資訊
- 善用 Debug Console 的即時執行功能
- 結合瀏覽器開發者工具進行全面除錯

### 🚀 進階技巧
- 使用條件中斷點 (右鍵中斷點設定條件)
- 監看變數 (Watch 面板)
- 呼叫堆疊分析 (Call Stack)
- 即時編輯程式碼 (Hot Reload)

## Tags
#vscode #debugging #debug-console #javascript #development-tools #api-testing #troubleshooting

## 問題類型
- 開發工具學習
- 除錯技巧
- 實用技能

## 解決狀態
- 教學內容完成 ✅
- 實際範例提供 ✅
- 最佳實踐整理完成 ✅
