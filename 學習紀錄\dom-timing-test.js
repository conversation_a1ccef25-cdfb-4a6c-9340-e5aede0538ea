// DOM 載入時機測試檔案
console.log("=== DOM 載入時機測試開始 ===");

// 測試 1: 立即獲取 DOM 元素（通常會失敗）
console.log("--- 測試 1: 立即獲取 ---");
const immediateElement = document.getElementById('search-form');
console.log("立即獲取結果:", immediateElement); // 通常是 null

// 測試 2: 檢查 document.readyState
console.log("--- 測試 2: 檢查文件狀態 ---");
console.log("當前文件狀態:", document.readyState);
// loading: 文件還在載入中
// interactive: 文件載入完成，但資源可能還在載入
// complete: 文件和所有資源都載入完成

// 測試 3: DOMContentLoaded 事件
console.log("--- 測試 3: DOMContentLoaded 事件 ---");
document.addEventListener('DOMContentLoaded', () => {
    console.log("🎉 DOMContentLoaded 事件觸發！");
    console.log("文件狀態:", document.readyState);
    
    const delayedElement = document.getElementById('search-form');
    console.log("延遲獲取結果:", delayedElement);
    
    if (delayedElement) {
        console.log("✅ 成功獲取 DOM 元素！");
        console.log("元素標籤:", delayedElement.tagName);
        console.log("元素 ID:", delayedElement.id);
    } else {
        console.log("❌ 仍然無法獲取 DOM 元素");
    }
});

// 測試 4: window.onload 事件
console.log("--- 測試 4: window.onload 事件 ---");
window.addEventListener('load', () => {
    console.log("🚀 window.onload 事件觸發！");
    console.log("文件狀態:", document.readyState);
    
    const fullyLoadedElement = document.getElementById('search-form');
    console.log("完全載入後獲取結果:", fullyLoadedElement);
});

// 測試 5: 變數生命週期示範
console.log("--- 測試 5: 變數生命週期 ---");

// 使用 let 的正確模式
let mySearchForm;
let myStatus = "未初始化";

console.log("初始狀態:", myStatus);
console.log("初始變數值:", mySearchForm); // undefined

const initializeElements = () => {
    mySearchForm = document.getElementById('search-form');
    myStatus = mySearchForm ? "初始化成功" : "初始化失敗";
    
    console.log("初始化後狀態:", myStatus);
    console.log("初始化後變數值:", mySearchForm);
};

// 安全的初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeElements);
} else {
    // DOM 已經載入完成
    initializeElements();
}

// 測試 6: 錯誤處理示範
console.log("--- 測試 6: 錯誤處理 ---");

const safeInitialization = () => {
    try {
        const elements = {
            searchForm: document.getElementById('search-form'),
            cityInput: document.getElementById('city-input'),
            weatherResult: document.getElementById('weather-result')
        };
        
        // 檢查所有必要元素
        const missingElements = Object.entries(elements)
            .filter(([name, element]) => !element)
            .map(([name]) => name);
        
        if (missingElements.length > 0) {
            throw new Error(`找不到以下元素: ${missingElements.join(', ')}`);
        }
        
        console.log("✅ 所有元素都成功獲取！");
        console.log("獲取的元素:", Object.keys(elements));
        
        return elements;
        
    } catch (error) {
        console.error("❌ 初始化錯誤:", error.message);
        return null;
    }
};

// 在 DOM 載入完成後執行安全初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log("執行安全初始化...");
    const elements = safeInitialization();
    
    if (elements) {
        console.log("可以開始設定事件監聽器了！");
    } else {
        console.log("初始化失敗，無法繼續設定應用程式");
    }
});

console.log("=== DOM 載入時機測試設定完成 ===");
console.log("請打開瀏覽器開發者工具查看測試結果");
