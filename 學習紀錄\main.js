// Weather API Application
// 天氣查詢應用程式主要邏輯

// API 配置
const WEATHER_API_KEY = '5592edd7014015b3ff5d0efedbb3690c';

// DOM 元素引用
let searchForm;
let cityInput;
let weatherResult;

// 錯誤處理函數
const displayError = (message) => {
    weatherResult.innerHTML = `
        <div class="error-message">
            <p>❌ ${message}</p>
            <p>請檢查城市名稱是否正確，或稍後再試。</p>
        </div>
    `;
};

// 載入中狀態
const showLoading = () => {
    weatherResult.innerHTML = `
        <div class="loading">
            <p>🔄 正在查詢天氣資訊...</p>
        </div>
    `;
};

// 表單提交處理
const handleWeatherSearch = async (event) => {
    event.preventDefault();

    const cityName = cityInput.value.trim();// 取得輸入框的值並去除頭尾空白

    if (!cityName) {
        displayError('請輸入城市名稱');
        return;
    }

    if (WEATHER_API_KEY === 'YOUR_API_KEY_HERE') {
        displayError('請先設定 API Key');
        return;
    }
    // 清空上一次的查詢結果，並顯示讀取中訊息
    weatherResult.innerHTML = `正在查詢 ${cityName} 的天氣...`;

    try {
        // --- 呼叫 API (兩步驟) ---
        // 第一步：取得經緯度
        const coords = await getCityCoordinates(cityName);
        if (!coords) {
            throw new Error(`找不到城市: ${cityName}`);
        }

        // 第二步：用經緯度取得天氣資料
        const weatherData = await getWeatherData(coords.lat, coords.lon);

        // --- 將資料渲染到畫面上 ---
        displayWeatherData(weatherData);

    } catch (error) {
        // --- 統一處理錯誤 ---
        weatherResult.innerHTML = `查詢失敗: ${error.message}`;
        console.error(error);
    }
};
/**
 * 根據城市名稱，取得該城市的經緯度
 */
async function getCityCoordinates(cityName) {
    const apiUrl = `http://api.openweathermap.org/geo/1.0/direct?q=${cityName},TW&limit=5&appid=${WEATHER_API_KEY}`;
    const response = await fetch(apiUrl);
    if (!response.ok) throw new Error('無法連接到地理編碼 API');
    const data = await response.json();
    if (data && data.length > 0) {
        return { lat: data[0].lat, lon: data[0].lon };
    }
    return null; // 找不到城市
}

/**
 * 根據經緯度，取得天氣資料
 */
async function getWeatherData(lat, lon) {
    // 注意！這裡的 API 連結不同，並且加上 units=metric 讓溫度顯示為攝氏
    const apiUrl = `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=${WEATHER_API_KEY}&units=metric`;
    const response = await fetch(apiUrl);
    if (!response.ok) throw new Error('無法連接到天氣 API');
    const data = await response.json();
    return data;
}

/**
 * 將天氣資料格式化並顯示在 HTML 中
 */
const displayWeatherData = (data) => {
    console.log(data);
    // 使用解構賦值從複雜的 API 回應中取出需要的資料
    const { name, main, weather, wind } = data;
    // 使用模板字串組合要顯示的 HTML 內容
    const htmlContent = `
    <h2>${name} 的天氣</h2>
    <p><strong>天氣狀況:</strong> ${weather[0].description}</p>
    <p><strong>溫度:</strong> ${main.temp}°C</p>
    <p><strong>體感溫度:</strong> ${main.feels_like}°C</p>
    <p><strong>濕度:</strong> ${main.humidity}%</p>
    <p><strong>風速:</strong> ${wind.speed} m/s</p>
  `;
    // 這就是關鍵！將組合好的 HTML 內容寫入 div 中
    weatherResult.innerHTML = htmlContent;
}

// 天氣資料處理函數
// const displayWeatherData = (data) => {
//     const { name, main, weather, wind } = data;
//     const temperature = Math.round(main.temp - 273.15); // 轉換為攝氏度
//     const description = weather[0].description;
//     const humidity = main.humidity;
//     const windSpeed = wind.speed;

//     const weatherHTML = `
//         <div class="weather-info">
//             <h2>${name}</h2>
//             <div class="temperature">${temperature}°C</div>
//             <div class="description">${description}</div>
//             <div class="details">
//                 <p>濕度: ${humidity}%</p>
//                 <p>風速: ${windSpeed} m/s</p>
//             </div>
//         </div>
//     `;

//     weatherResult.innerHTML = weatherHTML;
// };

// 初始化應用程式
const initWeatherApp = () => {
    // 取得 DOM 元素
    searchForm = document.getElementById('search-form');
    cityInput = document.getElementById('city-input');
    weatherResult = document.getElementById('weather-result');

    // 檢查必要元素是否存在
    if (!searchForm || !cityInput || !weatherResult) {
        console.error('找不到必要的 DOM 元素');
        return;
    }

    // 設定事件監聽器
    searchForm.addEventListener('submit', handleWeatherSearch);

    console.log('天氣應用程式初始化完成');
};

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', initWeatherApp);
